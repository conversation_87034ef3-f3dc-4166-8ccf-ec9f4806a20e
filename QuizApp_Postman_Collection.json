{"info": {"_postman_id": "quiz-app-collection", "name": "Quiz App API Collection", "description": "Complete API collection for testing the Quiz App endpoints including authentication, quiz operations, and user management.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('Registration successful:', response.message);", "    }", "} else {", "    console.log('Registration failed:', pm.response.json().message);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account"}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.token) {", "        pm.environment.set('authToken', response.token);", "        console.log('Login successful! <PERSON><PERSON> saved:', response.token);", "        console.log('Username:', response.username);", "    }", "} else {", "    console.log('<PERSON><PERSON> failed:', pm.response.json().message);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login with username and password to get JWT token"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/verify?token={{verificationToken}}", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify"], "query": [{"key": "token", "value": "{{verificationToken}}", "description": "Email verification token"}]}, "description": "Verify user email with verification token"}, "response": []}], "description": "Authentication related endpoints"}, {"name": "Quiz Operations", "item": [{"name": "Get Quiz Questions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/quiz/questions", "host": ["{{baseUrl}}"], "path": ["api", "quiz", "questions"]}, "description": "Get randomized quiz questions (requires authentication)"}, "response": []}, {"name": "Submit Quiz Answers", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"1\": \"Paris\",\n    \"2\": \"4\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/quiz/submit", "host": ["{{baseUrl}}"], "path": ["api", "quiz", "submit"]}, "description": "Submit quiz answers as key-value pairs where key is question ID and value is the selected answer"}, "response": []}, {"name": "Get User Score History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/quiz/history", "host": ["{{baseUrl}}"], "path": ["api", "quiz", "history"]}, "description": "Get the authenticated user's quiz score history"}, "response": []}], "description": "Quiz related operations"}, {"name": "Health Check", "item": [{"name": "Application Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/actuator/health", "host": ["{{baseUrl}}"], "path": ["actuator", "health"]}, "description": "Check if the application is running (if actuator is enabled)"}, "response": []}, {"name": "Root Endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}, "description": "Test basic connectivity to the application"}, "response": []}], "description": "Health check and connectivity tests"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "verificationToken", "value": "", "type": "string"}]}