# Quiz App - Postman Testing Guide

## 📋 Overview
This guide will help you test your Quiz App API using the provided Postman collection.

## 🚀 Quick Setup

### 1. Import the Collection
1. Open Postman
2. Click **Import** button
3. Select the `QuizApp_Postman_Collection.json` file
4. The collection will be imported with all endpoints ready to use

### 2. Environment Variables
The collection uses these variables (automatically configured):
- `baseUrl`: http://localhost:8080
- `authToken`: (automatically set after login)
- `verificationToken`: (manually set for email verification)

## 🧪 Testing Workflow

### Step 1: Health Check
Start by testing basic connectivity:
```
GET {{baseUrl}}/
```
Expected: 200 OK or 404 (both indicate the server is running)

### Step 2: Register a New User
```
POST {{baseUrl}}/api/auth/register
Body:
{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "password123"
}
```
Expected: 200 OK with success message

### Step 3: Login
```
POST {{baseUrl}}/api/auth/login
Body:
{
    "username": "testuser",
    "password": "password123"
}
```
Expected: 200 OK with JWT token
**Note**: The token is automatically saved to `authToken` variable

### Step 4: Get Quiz Questions
```
GET {{baseUrl}}/api/quiz/questions
Headers: Authorization: Bearer {{authToken}}
```
Expected: 200 OK with array of questions

### Step 5: Submit Quiz Answers
```
POST {{baseUrl}}/api/quiz/submit
Headers: Authorization: Bearer {{authToken}}
Body:
{
    "1": "Paris",
    "2": "4"
}
```
Expected: 200 OK with score response

### Step 6: Get Score History
```
GET {{baseUrl}}/api/quiz/history
Headers: Authorization: Bearer {{authToken}}
```
Expected: 200 OK with user's quiz history

## 🔧 Advanced Testing

### Testing Different Users
1. Change the username/email in registration
2. Login with different credentials
3. Test that users only see their own history

### Testing Invalid Scenarios
1. **Invalid Login**: Wrong username/password
2. **Unauthorized Access**: Remove Authorization header
3. **Invalid Token**: Modify the token value
4. **Invalid Quiz Answers**: Submit wrong question IDs

### Email Verification Testing
1. After registration, check your application logs for verification token
2. Copy the token and set it in the `verificationToken` variable
3. Use the "Verify Email" endpoint

## 📊 Expected Responses

### Successful Registration
```json
{
    "message": "User registered successfully. Please check your email for verification.",
    "success": true
}
```

### Successful Login
```json
{
    "message": "Login successful",
    "success": true,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "username": "testuser"
}
```

### Successful Email Verification
```json
{
    "message": "Account verified successfully",
    "success": true
}
```

### Error Response (e.g., Invalid Login)
```json
{
    "message": "Invalid username or password",
    "success": false
}
```

### Quiz Questions
```json
[
    {
        "id": 1,
        "text": "What is the capital of France?",
        "options": ["Paris", "London", "Berlin", "Madrid"]
    },
    {
        "id": 2,
        "text": "What is 2 + 2?",
        "options": ["2", "4", "6", "8"]
    }
]
```

### Quiz Submission Response
```json
{
    "score": 2,
    "totalQuestions": 2,
    "percentage": 100.0,
    "timestamp": "2025-08-13T00:33:32"
}
```

## 🐛 Troubleshooting

### Common Issues:

1. **Connection Refused**: Make sure your app is running on port 8080
2. **401 Unauthorized**: Check if the token is properly set
3. **404 Not Found**: Verify the endpoint URLs
4. **500 Internal Server Error**: Check application logs

### Debugging Tips:
1. Check the **Console** tab in Postman for automatic token saving
2. Use **Tests** tab to add custom validations
3. Check **Headers** tab to ensure Authorization is set correctly

## 🔄 Automation Scripts

The collection includes automatic token management:
- Login request automatically saves the JWT token
- All protected endpoints use the saved token
- No manual token copying required!

## 📝 Notes
- Make sure your Quiz App is running before testing
- The sample data includes 2 questions by default
- All timestamps are in ISO format
- Passwords should be at least 6 characters for security
