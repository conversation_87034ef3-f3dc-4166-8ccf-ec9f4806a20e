package com._cyrilc.quizApp.dto;

import com._cyrilc.quizApp.entity.Question;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuizResponse {
    private String message;
    private boolean success;
    private List<Question> questions;
    private int totalQuestions;

    // Constructor for successful quiz retrieval
    public QuizResponse(List<Question> questions) {
        this.message = "Quiz questions retrieved successfully";
        this.success = true;
        this.questions = questions;
        this.totalQuestions = questions.size();
    }

    // Constructor for error responses
    public QuizResponse(String message, boolean success) {
        this.message = message;
        this.success = success;
        this.totalQuestions = 0;
    }
}
