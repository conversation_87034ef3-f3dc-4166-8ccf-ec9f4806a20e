package com._cyrilc.quizApp;

import com._cyrilc.quizApp.entity.Question;
import com._cyrilc.quizApp.repository.QuestionRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class DataLoader {

    @Bean
    CommandLineRunner loadData(QuestionRepository questionRepository) {
        return args -> {
            questionRepository.save(new Question(null, "What is the capital of France?", new String[]{"Paris", "London", "Berlin", "Madrid"}, "Paris"));
            questionRepository.save(new Question(null, "What is 2 + 2?", new String[]{"2", "4", "6", "8"}, "4"));
        };
    }
}